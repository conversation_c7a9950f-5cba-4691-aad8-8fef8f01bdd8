import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { I18nManager, Image, Pressable, StyleSheet, Text, View } from "react-native";
import { ScrollView } from 'react-native-gesture-handler';
import RBSheet from 'react-native-raw-bottom-sheet';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch, useSelector } from 'react-redux';
import * as cartActions from '../../Store/Actions/cart';
import * as ordersActions from '../../Store/Actions/orders';
import Header from '../components/Header';
import LoadingMore from '../components/LoadingMore';
import MyFooter from '../components/MyFooter';
import { Black, DarkGrey, DarkYellow, MediumG<PERSON>, <PERSON>, <PERSON>1, <PERSON>, <PERSON><PERSON><PERSON>, appColor2, appFont, appFontBold, screenHeight, screenWidth } from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';

import BackgroundTimer from 'react-native-background-timer';


const CompleteOrder1 = props => {
    const [shared, setShared] = useState(props.route.params.shared);
    const [order, setOrder] = useState(props.route.params.order)
    const [timeMessage, setTimeMessage] = useState('الوقت المتوقع لتسليم طلبك من الساعة 2 ظهرآ الي الساعة 6 مساء')

    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);

    const minss = useSelector(state => state.cart.mins)
    const secss = useSelector(state => state.cart.secs)
    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)

    const orderTime = useSelector(state => state.orders.orderTime)
    const [time, setTime] = useState([]);
    const [timeTypes, setTimeTypes] = useState([
        {
            id: 1,
            name: strings('lang.Standarddelivery'),
            image: require('../images/newIcon/slow.png'),
            title: strings('lang.Youwillreceiveyourorderwithin48hours')
        },
        {
            id: 2,
            name: strings('lang.Expediteddelivery'),
            image: require('../images/newIcon/fast.png'),
            title: strings('lang.Youwillreceiveyourorderonthesameday')
        },
    ]);
    const [timeTypeName, setTimeTypeName] = useState('');
    const [timeTypeId, setTimeTypeId] = useState(0);

    const addresses = useSelector(state => state.addresses.addresses)
    const [Addresses, setAddresses] = useState(addresses);


    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState(cartt);
    const [cartItems, setCartItems] = useState(cartt.sheep);

    const [address, setAddress] = useState({});
    const [firstOrder, setFirstOrder] = useState({});
    const [timeId, setTimeId] = useState(null);

    const dispatch = useDispatch();
    const IsFocused = useIsFocused();
    const refRbSheet = useRef();

    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };
    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );

    // Format time as mm:ss
    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    };


    useEffect(() => {
        // setTime(orderTime)
        console.log('shared', shared);
        console.log('order', order);
        console.log('orderTime', orderTime);
        const getAddresss = async () => {
            const address = await AsyncStorage.getItem("address");
            addAddresss(JSON.parse(address));
            console.log('JSON.parse(address)', JSON.parse(address));
        }

        const getFirstOrder = async () => {
            try {
                setLoadingMore(true)
                let response = await dispatch(ordersActions.getFirstOrder());
                if (response.success == true) {
                    setFirstOrder(response.data)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoadingMore(false);
            } catch (err) {
                console.log('err', err)
                setLoadingMore(false);
            }
        };


        getAddresss();
        getFirstOrder();

    }, [IsFocused,]);
    useEffect(() => {
        const getOrderTimes = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOrderTimes(props.route.params.sheepTypeId));
                if (response.success == true) {
                    setTime(response.data)
                    console.log('props.route.params.sheepTypeId', props.route.params.sheepTypeId);
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getOrderTimes()

    }, [IsFocused,]);



    const addAddresss = async (addresss) => {
        setLoadingMore(true)
        console.log("item", addresss)
        AsyncStorage.setItem("address", JSON.stringify(addresss));

        setAddress(addresss);
        if (shared) {
            try {
                let response = await dispatch(cartActions.selectPartnerAddress(addresss.id ? addresss.id : address.id, order.id));
                if (response.success == true) {
                    setLoadingMore(false)
                }
                else {
                    if (response.message) {
                    }
                    setLoadingMore(false)
                }
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false)
            }
        } else {
            try {
                let response = await dispatch(cartActions.selectAddress(addresss.id ? addresss.id : address.id));
                if (response.success == true) {

                    setLoadingMore(false)
                }
                else {
                    if (response.message) {
                    }
                    setLoadingMore(false)
                }
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false)
            }
        }
    }
    const getCart = async () => {
        try {
            // setLoading(true)
            let token = await AsyncStorage.getItem('token')
            if (token) {
                let response = await dispatch(cartActions.getCart());
                if (response.success == true) {
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
            }
            // setLoading(false);
        } catch (err) {
            console.log('err', err)
            // setLoading(false);
        }
    };

    const nav = async () => {
        if (!address) {
            Toaster(
                'top',
                'danger',
                Red1,
                strings('lang.message10'),
                White,
                1500,
                screenHeight / 15,
            );
        }
        else if (cart.sheep && cart.sheep[0] && cart.sheep[0].sheep && cart.sheep[0].sheep.sharing_type && cart.sheep[0].sheep.sharing_type != null) {
            if (cart.is_first == true) {
                if (!shared && !timeId) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        strings('lang.message11'),
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                else {
                    setLoadingMore(true)
                    if (shared) {
                        console.log('1');

                    }
                    else {
                        console.log('4');

                        if (cart.is_first == true) {
                            console.log('2');

                            try {
                                let response = await dispatch(cartActions.selectOrderTime(timeId, timeTypeName));
                                if (response.success == true) {
                                }
                                else {
                                    if (response.message) {
                                        Toaster(
                                            'top',
                                            'danger',
                                            Red1,
                                            message,
                                            White,
                                            1500,
                                            screenHeight / 15,
                                        );
                                    }
                                }
                            }
                            catch (err) {
                                console.log('err', err)
                            }
                        } else {
                            console.log('3');

                        }

                    }

                    setLoadingMore(false)
                    setAddress({})
                    props.navigation.push("CompleteOrder2", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
                }
            } else {
                setLoadingMore(false)
                setAddress({})
                props.navigation.push("CompleteOrder2", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
            }
        } else {
            if (!shared && !timeId) {
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    strings('lang.message11'),
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
            else {
                setLoadingMore(true)
                if (shared) {

                }
                else {
                    // if (cart.is_first == true) {
                    console.log('5');

                    try {
                        let response = await dispatch(cartActions.selectOrderTime(timeId, timeTypeName));
                        if (response.success == true) {
                            console.log('6');
                        }
                        else {
                            if (response.message) {
                                Toaster(
                                    'top',
                                    'danger',
                                    Red1,
                                    message,
                                    White,
                                    1500,
                                    screenHeight / 15,
                                );
                            }
                        }
                    }
                    catch (err) {
                        console.log('err', err)
                    }
                    // } else {
                    //     console.log('7');

                    // }

                }

                setLoadingMore(false)
                setAddress({})
                props.navigation.push("CompleteOrder2", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
            }
        }
    }



    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')} backPress={() => { props.navigation.goBack() }} /> */}

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 100, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 20, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '50%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '95%', alignSelf: 'center', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '95%', alignSelf: 'center', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '95%', alignSelf: 'center', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>


                    < View style={{ marginTop: screenHeight / 40, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '90%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')} backPress={() => {
                    props.navigation.push('CompleteOrder0', { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
                }} /> */}

                {loadingMore ? <LoadingMore /> : <></>}

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >
                    <View style={{
                        flexDirection: 'row', width: "100%", alignSelf: "center", justifyContent: 'space-between', alignItems: "flex-end", height: screenHeight / 9,
                        backgroundColor: Red,
                    }}>
                        <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Cuttingandprocessing')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Titleanddate')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "33%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.payingoff')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, opacity: .5, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        {/* <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.Orderdetails')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, opacity: .5, marginTop: 5, borderRadius: 4 }}></View>
                        </View> */}
                    </View>

                    <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 20, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginVertical: 20 }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 35, }}>{strings('lang.messageFinshTime')} </Text>
                        <Image source={require('../images/newIcon/watch.png')} style={{ resizeMode: 'contain', width: '10%', height: '30%', tintColor: Red1 }} />
                        {/* <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{mins}:{secs < 10 && 0}{secs}</Text> */}
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{formatTime(timeLeft)}</Text>
                    </View>






                    <Text style={styles.title}>{strings('lang.Address')}</Text>
                    {address && address.address ?
                        <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', height: screenHeight / 12, backgroundColor: White, paddingHorizontal: '3%', justifyContent: "space-between", alignItems: "center", marginVertical: screenHeight / 100 }}>
                            <View style={{ width: '10%', height: '90%', alignItems: "center", alignSelf: 'center' }}>
                                <Image source={require('../images/modrek/location.png')} style={{ height: '100%', width: '80%', resizeMode: "contain", tintColor: Red }} />
                            </View>
                            <View style={{ width: '70%', height: '90%', alignSelf: 'center' }}>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '5%', alignSelf: 'flex-start' }}>{address.address}</Text>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '5%', alignSelf: 'flex-start' }}>{address.landmark ? address.landmark : ''} {address.landmark ? '-' : ''}  {address.building ? address.building : ''}</Text>
                            </View>
                            <View style={{ width: '20%', height: '90%', alignItems: "center", justifyContent: "center" }}>
                                <Button style={{ backgroundColor: Red, width: '97%', height: screenHeight / 20, alignSelf: 'flex-end', alignItems: "center", justifyContent: "center", borderRadius: 20 }}
                                    onPress={() => refRbSheet.current.open()} >
                                    <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White }}>{strings('lang.Change')}</Text>
                                </Button>
                            </View>
                        </View>
                        :
                        <Button onPress={() => { props.navigation.push("Addresses", { screen: 'CompleteOrder1', shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }} style={{ backgroundColor: WhiteGery, width: "90%", marginBottom: '10%', alignSelf: "center", alignItems: "center", justifyContent: "center", borderRadius: 20, opacity: .8 }}>
                            <Image source={require('../images/modrek/location.png')} style={{ width: "10%", height: "60%", resizeMode: "contain", tintColor: Red }} />
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black }}>{strings('lang.addaddress')}</Text>
                        </Button>
                    }

                    {shared
                        ?
                        <></>
                        :
                        <>
                            {cart.sheep && cart.sheep[0] && cart.sheep[0].sheep && cart.sheep[0].sheep.sharing_type != null
                                ?
                                <>
                                    {cart && cart.is_first === true
                                        ?
                                        <>
                                            <Text style={styles.title}>{strings('lang.Choosethedeliverymethod')}</Text>
                                            {timeTypes && timeTypes.map((item, index) => {
                                                return (
                                                    index == 0

                                                        ?
                                                        <Pressable
                                                            onPress={() => {
                                                                setTimeTypeId(item.id)
                                                                setTimeTypeName(item.id == 1 ? 'normal' : 'fast')
                                                                console.log('image', item.image);
                                                                setTimeId(null)

                                                            }}
                                                            style={[styles.timeTypeContainer, { backgroundColor: timeTypeId == item.id ? '#B3BFC4' : White }]}>
                                                            <View style={{
                                                                flexDirection: 'row',
                                                                alignItems: 'center',
                                                                justifyContent: 'space-between',
                                                                width: '100%',
                                                                height: '100%'
                                                            }}>

                                                                <View style={[styles.viewImageContainer, {
                                                                    backgroundColor: timeTypeId == item.id ?
                                                                        White
                                                                        :
                                                                        '#dfe5e8'
                                                                }]}>
                                                                    <Image source={item.image} style={{
                                                                        width: '80%', height: '80%', resizeMode: 'contain',
                                                                    }} />
                                                                </View>
                                                                <View style={styles.textTimeTypeContainer}>
                                                                    <Text style={[styles.textTimeType, { color: Red1 }]}>{item.name}</Text>
                                                                    <Text style={styles.textTimeType}>{item.title}</Text>
                                                                </View>
                                                                <Image source={require('../images/newIcon/row.png')} style={{
                                                                    width: '10%', height: '20%', resizeMode: 'contain',
                                                                    position: 'absolute',
                                                                    right: 4,
                                                                    top: 3
                                                                }} />
                                                            </View>

                                                        </Pressable>
                                                        :
                                                        <></>


                                                )
                                            },)}

                                            {timeTypeId ?
                                                <View style={[styles.viewTimeContainer, {
                                                    borderRadius: timeId ? 0 : 20
                                                }]}>
                                                    <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'space-between', aliignItems: 'center' }}>
                                                        <View style={{ width: '70%', }}>
                                                            <Text style={styles.title}>{strings('lang.Determiningthetimeofentrytotheslaughterhouse')}</Text>
                                                            <View style={{ flexDirection: 'row', width: '100%', alignSelf: "center", justifyContent: 'space-between', flexWrap: 'wrap' }}>
                                                                {time && time.map((item, index) => {
                                                                    return (
                                                                        <Pressable
                                                                            onPress={() => { setTimeId(item.id) }}
                                                                            disabled={item.is_past == true ? true : false}
                                                                            style={{
                                                                                width: '45%',
                                                                                height: screenHeight / 20,
                                                                                borderWidth: 1,
                                                                                borderColor: item.is_past == true ? MediumGrey : WhiteGery,
                                                                                borderRadius: 20,
                                                                                marginBottom: 10,
                                                                                justifyContent: "center",
                                                                                alignItems: "center",
                                                                                backgroundColor: item.is_past == true ? MediumGrey : item.id == timeId ? Red : WhiteGery,
                                                                                opacity: item.is_past == true ? 0.5 : null
                                                                            }}>
                                                                            <Text style={[item.id == timeId ? styles.textWhite : styles.textGrey, { color: item.is_available == false ? DarkGrey : item.id == timeId ? White : Black }]}>{item.time}</Text>
                                                                        </Pressable>
                                                                    )
                                                                })}
                                                            </View>
                                                        </View>
                                                        <Image source={require('../images/newIcon/farm.png')} style={{
                                                            width: '20%', height: screenHeight / 10, resizeMode: 'contain',

                                                        }} />
                                                    </View>
                                                </View>
                                                :
                                                <></>
                                            }

                                        </>
                                        :
                                        <>
                                            {firstOrder && firstOrder.order_time &&
                                                <>
                                                    <View style={{ backgroundColor: WhiteGery, padding: 5, borderRadius: 20, alignSelf: 'center', width: '70%', alignSelf: "center", justifyContent: 'center', marginBottom: 10 }}>
                                                        <Text style={[styles.textWhite, { color: Black, textAlign: 'center' }]}>{`${strings('lang.timeMessage')} ${firstOrder.order_time.time}`}</Text>
                                                    </View>
                                                    {firstOrder.order_time.id == 19 || firstOrder.order_time.id == 33 || firstOrder.order_time.id == 32
                                                        ?
                                                        <View style={{ flexDirection: 'row', backgroundColor: Red, width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', padding: 10, borderRadius: 15 }}>
                                                            {/* <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White }}>{strings('lang.message19')} {`3:30 ${strings('lang.to')} 4:30`}</Text> */}
                                                            <View style={{ width: '70%' }}>
                                                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{timeMessage}</Text>
                                                            </View>
                                                            <Image source={require('../images/newIcon/timer.png')} style={{
                                                                width: '20%', height: screenHeight / 10, resizeMode: 'contain',
                                                            }} />
                                                        </View>
                                                        :
                                                        <></>
                                                    }
                                                    {firstOrder.order_time.id == 35 || firstOrder.order_time.id == 36 || firstOrder.order_time.id == 37
                                                        ?
                                                        <View style={{ flexDirection: 'row', backgroundColor: Red, width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', padding: 10, borderRadius: 15 }}>
                                                            {/* <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White }}>{strings('lang.message19')} {`3:30 ${strings('lang.to')} 4:30`}</Text> */}
                                                            <View style={{ width: '70%' }}>
                                                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>
                                                                    {'الوقت المتوقع لتسليم طلبك من الساعة 7 مساء الي الساعة 11 مساء'}</Text>
                                                            </View>
                                                            <Image source={require('../images/newIcon/timer.png')} style={{
                                                                width: '20%', height: screenHeight / 10, resizeMode: 'contain',

                                                            }} />
                                                        </View>
                                                        :
                                                        <></>
                                                    }
                                                </>
                                            }

                                        </>
                                    }
                                </>
                                :
                                <>

                                    {/* <Text style={styles.title}>{strings('lang.Determiningthetimeofentrytotheslaughterhouse')}</Text>
                                    <View style={{ flexDirection: 'row', width: '90%', alignSelf: "center", justifyContent: 'space-between', flexWrap: 'wrap' }}>
                                        {time && time.map((item, index) => {
                                            return (
                                                <Pressable
                                                    onPress={() => { setTimeId(item.id) }}
                                                    disabled={item.is_past == true ? true : false}
                                                    style={{
                                                        width: screenWidth / 3.6,
                                                        height: screenHeight / 20,
                                                        borderWidth: 1,
                                                        borderColor: item.is_past == true ? MediumGrey : WhiteGery,
                                                        borderRadius: 20,
                                                        marginBottom: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        backgroundColor: item.is_past == true ? MediumGrey : item.id == timeId ? Red : WhiteGery,
                                                        opacity: item.is_past == true ? 0.5 : null
                                                    }}>
                                                    <Text style={[item.id == timeId ? styles.textWhite : styles.textGrey, { color: item.is_available == false ? DarkGrey : item.id == timeId ? White : Black }]}>{item.time}</Text>
                                                </Pressable>
                                            )
                                        })}
                                    </View> */}

                                    <Text style={styles.title}>{strings('lang.Choosethedeliverymethod')}</Text>
                                    {timeTypes && timeTypes.map((item, index) => {
                                        return (
                                            <Pressable
                                                onPress={() => {
                                                    setTimeTypeId(item.id)
                                                    setTimeTypeName(item.id == 1 ? 'normal' : 'fast')
                                                    console.log('image', item.image);
                                                    setTimeId(null)

                                                }}
                                                style={[styles.timeTypeContainer, { backgroundColor: timeTypeId == item.id ? '#B3BFC4' : White }]}>
                                                <View style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'space-between',
                                                    width: '100%',
                                                    height: '100%'
                                                }}>

                                                    <View style={[styles.viewImageContainer, {
                                                        backgroundColor: timeTypeId == item.id ?
                                                            White
                                                            :
                                                            '#dfe5e8'
                                                    }]}>
                                                        <Image source={item.image} style={{
                                                            width: '80%', height: '80%', resizeMode: 'contain',
                                                        }} />
                                                    </View>
                                                    <View style={styles.textTimeTypeContainer}>
                                                        <Text style={[styles.textTimeType, { color: Red1 }]}>{item.name}</Text>
                                                        <Text style={styles.textTimeType}>{item.title}</Text>
                                                    </View>
                                                    <Image source={require('../images/newIcon/row.png')} style={{
                                                        width: '10%', height: '20%', resizeMode: 'contain',
                                                        position: 'absolute',
                                                        right: 4,
                                                        top: 3
                                                    }} />
                                                </View>
                                                {index == 1 ?
                                                    <Image source={require('../images/newIcon/type2.png')} style={{
                                                        width: '35%', height: '30%', resizeMode: 'contain',
                                                        position: 'absolute',
                                                        right: 4,
                                                        bottom: 3
                                                    }} />
                                                    :
                                                    <></>
                                                }
                                            </Pressable>
                                        )
                                    },)}

                                    {timeTypeId ?
                                        <View style={[styles.viewTimeContainer, {
                                            borderRadius: timeId ? 0 : 20
                                        }]}>
                                            <View style={{ width: '90%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'space-between', aliignItems: 'center' }}>
                                                <View style={{ width: '70%', }}>
                                                    <Text style={styles.title}>{strings('lang.Determiningthetimeofentrytotheslaughterhouse')}</Text>
                                                    <View style={{ flexDirection: 'row', width: '100%', alignSelf: "center", justifyContent: 'space-between', flexWrap: 'wrap' }}>
                                                        {time && time.map((item, index) => {
                                                            return (
                                                                <Pressable
                                                                    onPress={() => { setTimeId(item.id) }}
                                                                    disabled={item.is_past == true ? true : false}
                                                                    style={{
                                                                        width: '45%',
                                                                        height: screenHeight / 20,
                                                                        borderWidth: 1,
                                                                        borderColor: item.is_past == true ? MediumGrey : WhiteGery,
                                                                        borderRadius: 20,
                                                                        marginBottom: 10,
                                                                        justifyContent: "center",
                                                                        alignItems: "center",
                                                                        backgroundColor: item.is_past == true ? MediumGrey : item.id == timeId ? Red : WhiteGery,
                                                                        opacity: item.is_past == true ? 0.5 : null
                                                                    }}>
                                                                    <Text style={[item.id == timeId ? styles.textWhite : styles.textGrey, { color: item.is_available == false ? DarkGrey : item.id == timeId ? White : Black }]}>{item.time}</Text>
                                                                </Pressable>
                                                            )
                                                        })}
                                                    </View>
                                                </View>
                                                <Image source={require('../images/newIcon/farm.png')} style={{
                                                    width: '20%', height: screenHeight / 10, resizeMode: 'contain',

                                                }} />
                                            </View>
                                        </View>
                                        :
                                        <></>
                                    }
                                </>
                            }
                        </>
                    }

                    {timeId
                        ?
                        timeId == 19 || timeId == 33 || timeId == 32
                            ?
                            <View style={{
                                backgroundColor: Red, width: '85%', alignSelf: 'center',
                                padding: 10,
                                borderBottomLeftRadius: 20,
                                borderBottomRightRadius: 20,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}>
                                <View style={{ width: '70%' }}>
                                    <Text
                                        style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>
                                        {timeMessage}</Text>
                                </View>
                                <Image source={require('../images/newIcon/timer.png')} style={{
                                    width: '20%', height: screenHeight / 10, resizeMode: 'contain',

                                }} />
                            </View>
                            :
                            <></>
                        :
                        <></>
                    }
                    {timeId
                        ?
                        timeId == 35 || timeId == 37 || timeId == 36
                            ?
                            <View style={{
                                backgroundColor: Red, width: '85%', alignSelf: 'center', padding: 10,
                                borderBottomLeftRadius: 20,
                                borderBottomRightRadius: 20,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}>
                                <View style={{ width: '70%' }}>
                                    <Text
                                        style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>
                                        {'الوقت المتوقع لتسليم طلبك من الساعة 7 مساء الي الساعة 11 مساء'}</Text>
                                </View>
                                <Image source={require('../images/newIcon/timer.png')} style={{
                                    width: '20%', height: screenHeight / 10, resizeMode: 'contain',

                                }} />
                            </View>
                            :
                            <></>
                        :
                        <></>
                    }




                    <View style={{ flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse', alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, marginVertical: '10%' }}>
                        <Button onPress={() => { props.navigation.push('CompleteOrder0', { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Image source={require('../images/modrek/arrowRight.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: Black, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center', transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: Black, marginHorizontal: '1%' }}>{strings('lang.back')}</Text>
                        </Button>
                        <Button onPress={() => nav()} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10 }}>
                            <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center', transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.next')}</Text>
                        </Button>
                    </View>

                </ScrollView>

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 1.4}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            // width: '25%',
                            // backgroundColor: DarkGrey
                        }
                    }}
                // closeOnDragDown={true}
                >
                    <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'center', width: '100%', paddingVertical: '3%', }}>

                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, }}>اختر عنوان</Text>
                        <View style={{ width: '100%', height: '.5%', backgroundColor: WhiteGery, marginVertical: '3%' }}></View>
                        <ScrollView style={{ width: '100%', alignSelf: 'center' }}>
                            {Addresses.map((item, index) => {
                                return (
                                    <Pressable
                                        onPress={() => { refRbSheet.current.close(); addAddresss(item) }}
                                        style={{ flexDirection: 'column', width: '100%', borderBottomColor: MediumGrey, borderBottomWidth: 1, height: screenHeight / 9 }}>
                                        <View style={{ flexDirection: 'row', width: '90%', alignItems: 'flex-start', justifyContent: 'center', alignSelf: 'center', }}>
                                            <Image style={{ height: '45%', width: '10%', resizeMode: "contain", tintColor: Red }} source={require('../images/modrek/location.png')} />
                                            <View style={{ flexDirection: 'column', width: '90%', marginHorizontal: '2%', alignItems: 'flex-start' }}>
                                                <Text style={{ fontFamily: appFont, fontSize: screenWidth / 25, color: Black }}>{item.address}</Text>
                                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{item.landmark ? item.landmark : ''} {item.landmark ? '-' : ''}  {item.building ? item.building : ''}</Text>
                                            </View>
                                        </View>
                                    </Pressable>
                                )
                            })}
                            {loadingMore ? <LoadingMore /> : <></>}
                        </ScrollView>

                        <View style={{ flexDirection: 'row', alignItems: "center", justifyContent: "center", width: '90%', alignSelf: "center", marginBottom: screenHeight / 5, height: screenHeight / 12 }}>
                            <Button onPress={() => { refRbSheet.current.close(); props.navigation.push("Map", { screen: 'CompleteOrder1', cart: 'true', shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }} style={{ width: '90%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: screenHeight / 15 }}>
                                <Image source={require('../images/modrek/location.png')} style={{ resizeMode: 'contain', width: '10%', height: '65%', alignItems: 'center', tintColor: White }} />
                                <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{'اضف عنوان'}</Text>
                            </Button>
                        </View>

                    </View>

                </RBSheet>


                <View style={{ height: screenHeight / 10, }}></View>
                {/* <MyFooter navigation={props.navigation} /> */}

            </View>

        )
    }
}

export default CompleteOrder1;
const styles = StyleSheet.create({
    textTimeType: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 33,
        color: Black,
        textAlign: I18nManager.isRTL ? 'left' : 'right'
    },
    textTimeTypeContainer: {
        width: '65%',
        height: '100%',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        alignSelf: 'center',
        paddingVertical: '3%'
    },
    viewTimeContainer: {
        backgroundColor: White,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        alignSelf: 'center',
        width: '85%',
        alignSelf: "center",
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    timeTypeContainer: {
        backgroundColor: White,
        padding: 5, borderRadius: 10,
        alignSelf: 'center',
        width: '90%',
        alignItems: "center",
        justifyContent: 'space-between',
        height: screenHeight / 8,
        marginBottom: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        flexDirection: 'row'
    },
    viewImageContainer: {
        width: '30%', height: '90%', overflow: 'hidden', alignItems: 'center', justifyContent: 'center',
        alignSelf: 'center',
        borderRadius: 5,
        borderColor: '#b5bfc4',
        borderWidth: 1,
        backgroundColor: '#dfe5e8',
        // opacity: .6
    },
    skipContainer: {
        marginVertical: screenHeight / 20,
        flexDirection: 'row',
        alignSelf: "center"
    },
    skipText: {
        fontSize: screenWidth / 25,
        fontFamily: appFontBold,
        color: appColor2,
        alignSelf: 'center',
        marginHorizontal: 5
    },
    buttonContainer: {
        backgroundColor: appColor2,
        width: screenWidth / 1.2,
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 10,
        marginTop: screenHeight / 8
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    textWhite: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 32
    },
    textWhiteBig: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    textBlack: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    textGrey: {
        color: DarkGrey,
        fontFamily: appFontBold,
        fontSize: screenWidth / 32
    },
});