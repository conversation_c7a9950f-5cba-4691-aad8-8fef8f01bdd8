import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, BackHandler, FlatList, I18nManager, Image, Keyboard, Modal, Platform, Pressable, RefreshControl, ScrollView, StyleSheet, Text, View } from "react-native";
import {
    CopilotStep,
    copilot,
    walkthroughable,
} from "react-native-copilot";
import RBSheet from 'react-native-raw-bottom-sheet';
import StarRating from 'react-native-star-rating';
import { VLCPlayer } from 'react-native-vlc-media-player';
import { useDispatch, useSelector } from 'react-redux';
import * as addressesActions from '../../Store/Actions/addresses';
import * as authActions from '../../Store/Actions/auth';
import * as cartActions from '../../Store/Actions/cart';
import * as farmesActions from '../../Store/Actions/farms';
import * as ordersActions from '../../Store/Actions/orders';
import * as settingsActions from '../../Store/Actions/settings';
import CircularProgress3 from '../components/CircularProgress3';
import FarmContainer from '../components/FarmContainer';
import LoadingMore from '../components/LoadingMore';
import MyComponent from '../components/MyComponent';
import { Black, DarkGrey, DarkYellow, LightGreen, MediumGrey, Red, Red1, White, WhiteGery, WhiteYellow, appFont, appFontBold, appFontNawar, appFontTajawal, screenHeight, screenWidth } from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
export const CLEARTIMEROPTION = 'CLEARTIMEROPTION';
export const CLEAROPTIONS = 'CLEAROPTIONS';


const Home = props => {

    const [search, setSearch] = useState('');
    const [Token, setToken] = useState('');
    const [weightRangeId, setWeightRangeId] = useState(null);
    const [weightName, setWeightName] = useState('');
    const [weightRange, setWeightRange] = useState([]);
    const [Sacrifices, setSacrifices] = useState([]);
    const [sacrificeId, setSacrificeId] = useState(0);
    const [sheepcategories, setSheepcategories] = useState([]);
    const [sheepcategoriesId, setSheepcategoriesId] = useState(null);
    const [categoryName, setCategoryName] = useState('');
    const [page, setPage] = useState(1);
    const farmss = useSelector(state => state.farms.farms)
    const [farmId, setFarmId] = useState(0);
    const [typeName, setTypeName] = useState('');
    const [sheetType, setSheetType] = useState('');
    const [link, setLink] = useState('')
    const [farms, setFarms] = useState([]);
    const [item, setItem] = useState({});
    const [sheep, setSheep] = useState({});
    const [market_avg_price, setMarket_avg_price] = useState('');
    const [logo, setLogo] = useState('');
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [Reload, setReload] = useState(false);
    const [loadingMoreFarms, setLoadingMoreFarms] = useState(false)
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [activeType, setActiveType] = useState(false);
    const [noResault, setNoResault] = useState('');
    const [activeLive, setActiveLive] = useState(0);
    const [iddd, setIddd] = useState(0);
    const [viewId, setViewId] = useState(0);
    const [scrollToIndexx, setScrollToIndexx] = useState(0);
    const [visible, setIsVisible] = useState(false);
    const [sheet, setSheet] = useState(false);
    const [sheet1, setSheet1] = useState(false);
    const dispatch = useDispatch();
    const [sheepImages, setSheepImages] = useState([]);

    const [imageUrl, setImageUrl] = useState('https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg');

    const refRbSheet = useRef();
    const refRbSheetTypes = useRef();
    const IsFocused = useIsFocused();
    const flatListRef = useRef(null);
    const intervalRef = useRef(null); // Store interval ID in a ref



    useEffect(() => {
        console.log('farmss', farmss);
        console.log('props.route.params.scrollToIndex', props.route.params.scrollToIndex);
        setScrollToIndexx(props.route.params.scrollToIndex)
        const performTaskAndStopTimer = () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current); // Stop the interval
                intervalRef.current = null; // Reset the interval reference
                console.log('Interval stopped automatically.');
            }
            console.log('Task performed after stopping interval.');
        };
        const scrollToIndexxx = async () => {
            if (scrollToIndexx !== undefined && flatListRef.current) {
                try {
                    console.log('done');
                    flatListRef.current.scrollToIndex({
                        index: scrollToIndexx,
                        animated: true,
                    });
                } catch (error) {
                    console.error('Invalid index passed to scrollToIndex:', error);
                }
            }
        };
        intervalRef.current = setInterval(() => {
            scrollToIndexxx()
            setTimeout(() => {
                performTaskAndStopTimer()
            }, 5000);
        }, 1000); // 60000ms = 1 minute

        // // Cleanup interval on component unmount
        return () => {
            clearInterval(intervalRef.current);

        };
    }, [scrollToIndexx]);

    useEffect(() => {

        const fcm_token = async () => {
            setLoading(true)
            // await messaging().registerDeviceForRemoteMessages();
            const token = await messaging().getToken();

            console.log('getTokenHome', token);
            try {
                let response = await dispatch(authActions.fcm_token(token));
                if (response.success == true) {
                    setLoading(false)
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
            }
            catch (err) {
                console.log('err', err)
            }
            setLoading(false)
        }

        const user = async () => {
            let response = await dispatch(authActions.user());
            console.log('response', response)
        }
        const token = async () => {
            let token = await AsyncStorage.getItem('token')
            if (token) {
                user();
                fcm_token()
                getCart();
                getOptions();
                getAddresses();
                getPaymentMethods();
                getOrderTime();
            }
            console.log('ggggg', token);
        }
        token()
        const getSheepCategoriesWeights = async () => {
            setLoading(true)
            let response = await dispatch(farmesActions.getSheepCategories());
            if (response.success == true) {

                setSheepcategories(response.data)
                if (props.route.params.sheepCategory) {
                    setSheepcategoriesId(props.route.params.sheepCategory)
                    getWeights(props.route.params.sheepCategory)
                }
                else {
                    setSheepcategoriesId(null)
                    getWeights(null)
                }
                //     setLoading(true)
                //     let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
                //     if (response1.success == true) {
                //         setWeightRange(response1.data)
                //         if (props.route.params.weightId) {
                //             setWeightRangeId(props.route.params.weightId)
                //         }
                //         else {
                //             setWeightRangeId(response1.data[0].id)
                //         }
                //         getFarms(props.route.params.sheepCatId ? props.route.params.sheepCatId : response.data[0].id, props.route.params.weightId ? props.route.params.weightId : response1.data[0].id);
                //     }
                //     else {
                //         if (response1.message) {
                //             Toaster(
                //                 'top',
                //                 'danger',
                //                 Red,
                //                 response1.message,
                //                 White,
                //                 1500,
                //                 screenHeight / 15,
                //             );
                //         }
                //     }
                //     setLoading(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }
        };

        const getWeights = async (sheepcategoriesId) => {
            setLoading(true)

            let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
            if (response1.success == true) {
                setWeightRange(response1.data)

                if (props.route.params.weightId) {
                    setWeightRangeId(props.route.params.weightId)
                    getSharingTypes(sheepcategoriesId, props.route.params.weightId)

                }
                else {
                    setWeightRangeId(null)
                    getSharingTypes(sheepcategoriesId, null)
                }
                setLoading(false);
            }
            else {
                if (response1.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response1.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }

        };

        const getSharingTypes = async (sheepcategoriesId, weightRangeId) => {
            setLoading(true)

            let response1 = await dispatch(farmesActions.getSharingTypes());
            if (response1.success == true) {
                setSacrifices(response1.data)
                if (props.route.params.sheepTypeId) {
                    setSacrificeId(props.route.params.sheepTypeId)
                    setTypeName(props.route.params.typeName)
                    getFarms(sheepcategoriesId, weightRangeId, props.route.params.sheepTypeId)
                } else {
                    setSacrificeId(response1.data[2].id)
                    setTypeName(response1.data[2].name)
                    getFarms(sheepcategoriesId, weightRangeId, response1.data[2].id)
                    setActiveType(
                        item.id == 1 ?
                            false
                            :
                            true
                    );
                }
                setLoading(false);
            }
            else {
                if (response1.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response1.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }

        };

        const getFarms = async (sheepcategoriesId, weightRangeId, sacrificeId) => {
            try {
                setLoading(true)
                let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, 1, search, sacrificeId));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setFarms([]);
                        setMarket_avg_price(0);
                        setPageNumber(1)
                        setLastPageNumber(1)
                        // setLogo('')
                        // setLink('')
                        // setFarmId(0)
                    }
                    else {
                        if (props.route.params.farmId) {
                            setLogo(props.route.params.logo)
                            setLink(props.route.params.link && props.route.params.link)
                            // setLink(farmss[0] && farmss[0].camera_url)
                            setFarmId(props.route.params.farmId)
                            setCategoryName(props.route.params.sheepCategoryName)
                            setTypeName(props.route.params.sheepTypeName)
                            setWeightName(props.route.params.sheepWeightName)
                            // setSacrificeId(sacrificeId)
                            setActiveType(
                                sacrificeId == 1 ?
                                    false
                                    :
                                    true
                            );
                        } else {
                            setLogo(farmss[0] && farmss[0].image)
                            setLink(farmss[0] && farmss[0].camera_url)
                            setMarket_avg_price(response.data.market_avg_price)
                            setPageNumber(1)
                            setFarmId(farmss[0] && farmss[0].id)
                        }
                        setFarms(response.data.items)


                        setLastPageNumber(response.data.last_page)
                        setNoResault('');
                    }
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getCart = async () => {
            try {
                setLoading(true)
                let response = await dispatch(cartActions.getCart());
                if (response.success == true) {
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getOrderTime = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOrderTimes(1));
                if (response.success == true) {
                    dispatch({ type: CLEARTIMEROPTION, })
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getAddresses = async () => {
            try {
                setLoadingMore(true)
                let response = await dispatch(addressesActions.getAddresses());
                if (response.success == true) {
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoadingMore(false);
            } catch (err) {
                console.log('err', err)
                setLoadingMore(false);
            }
        };

        const getPaymentMethods = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getPaymentMethods());
                if (response.success == true) {
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getOptions = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOptions(1));
                if (response.success == true) {
                    dispatch({ type: CLEAROPTIONS, })

                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getSheepCategoriesWeights();
        getSettings();

    }, [Reload,]);

    useEffect(() => {
        const token = async () => {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        token()
    }, [Reload,]);

    const getWeights = async (sheepcategoriesId,) => {
        setLoadingMore(true)

        let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
        if (response1.success == true) {
            setWeightRange(response1.data)
            setWeightRangeId(null);
            getFarmsWeight(sheepcategoriesId, null, 1, sacrificeId)
            setLoadingMore(false);
        }
        else {
            if (response1.message) {
                Toaster(
                    'top',
                    'danger',
                    Red,
                    response1.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
            setLoadingMore(false);
        }

    };

    const getFarmsWeight = async (sheepcategoriesId, weightRangeId, page, sacrificeId,) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, page, search, sacrificeId));
            if (response.success == true) {
                if (response.data.items.length == 0) {
                    setNoResault(strings('lang.No_Results'));
                    setFarms([]);
                    setMarket_avg_price(0);
                    setPageNumber(1)
                    setLastPageNumber(1)
                    // setLogo('')
                    // setLink('')
                    // setFarmId(0)
                }
                else {
                    setPageNumber(1)
                    setMarket_avg_price(response.data.market_avg_price)
                    setFarms(response.data.items)
                    setLastPageNumber(response.data.last_page)
                    setNoResault('');
                    if (props.route.params.farmId) {
                        setLogo(props.route.params.logo)
                        setLink(props.route.params.link && props.route.params.link)
                        setFarmId(props.route.params.farmId)
                        setScrollToIndexx(props.route.params.scrollToIndex)
                    } else {
                        setLogo(farmss[0] && farmss[0].image)
                        setLink(farmss[0] && farmss[0].camera_url)
                        setFarmId(farmss[0] && farmss[0].id)
                    }

                }
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoadingMore(false)
            setLoading(false);
        }
    };

    const getOptions = async (sharingTypeId) => {
        try {
            setLoading(true)
            let response = await dispatch(ordersActions.getOptions(sharingTypeId));
            if (response.success == true) {
                dispatch({ type: CLEAROPTIONS, })
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const getOrderTime = async (sharingTypeId) => {
        try {
            setLoading(true)
            let response = await dispatch(ordersActions.getOrderTimes(sharingTypeId));
            if (response.success == true) {
                dispatch({ type: CLEARTIMEROPTION, })

            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };


    const getSettings = async () => {
        try {
            setLoading(true)
            let response = await dispatch(settingsActions.getSettings());
            if (response.success == true) {

            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    };

    const onRefresh = async => {

        const getSheepCategoriesWeights = async () => {
            setLoading(true)
            let response = await dispatch(farmesActions.getSheepCategories());
            if (response.success == true) {

                setSheepcategories(response.data)
                if (props.route.params.sheepCategory) {
                    setSheepcategoriesId(props.route.params.sheepCategory)
                    getWeights(props.route.params.sheepCategory)
                }
                else {
                    setSheepcategoriesId(null)
                    getWeights(null)
                }
                //     setLoading(true)
                //     let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
                //     if (response1.success == true) {
                //         setWeightRange(response1.data)
                //         if (props.route.params.weightId) {
                //             setWeightRangeId(props.route.params.weightId)
                //         }
                //         else {
                //             setWeightRangeId(response1.data[0].id)
                //         }
                //         getFarms(props.route.params.sheepCatId ? props.route.params.sheepCatId : response.data[0].id, props.route.params.weightId ? props.route.params.weightId : response1.data[0].id);
                //     }
                //     else {
                //         if (response1.message) {
                //             Toaster(
                //                 'top',
                //                 'danger',
                //                 Red,
                //                 response1.message,
                //                 White,
                //                 1500,
                //                 screenHeight / 15,
                //             );
                //         }
                //     }
                //     setLoading(false);
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }
        };
        getSheepCategoriesWeights()
        const getWeights = async (sheepcategoriesId) => {
            setLoading(true)

            let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
            if (response1.success == true) {
                setWeightRange(response1.data)

                if (props.route.params.weightId) {
                    setWeightRangeId(props.route.params.weightId)
                    getSharingTypes(sheepcategoriesId, props.route.params.weightId)

                }
                else {
                    setWeightRangeId(null)
                    getSharingTypes(sheepcategoriesId, null)
                }
                setLoading(false);
            }
            else {
                if (response1.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response1.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }

        };

        const getSharingTypes = async (sheepcategoriesId, weightRangeId) => {
            setLoading(true)

            let response1 = await dispatch(farmesActions.getSharingTypes());
            if (response1.success == true) {
                setSacrifices(response1.data)
                if (props.route.params.sheepTypeId) {
                    setSacrificeId(props.route.params.sheepTypeId)
                    setTypeName(props.route.params.typeName)
                    getFarms(sheepcategoriesId, weightRangeId, props.route.params.sheepTypeId)
                } else {
                    setSacrificeId(response1.data[2].id)
                    setTypeName(response1.data[2].name)
                    getFarms(sheepcategoriesId, weightRangeId, response1.data[2].id)
                    setActiveType(
                        item.id == 1 ?
                            false
                            :
                            true
                    );
                }
                setLoading(false);
            }
            else {
                if (response1.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response1.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }

        };

        const getFarms = async (sheepcategoriesId, weightRangeId, sacrificeId) => {
            try {
                setLoading(true)
                let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, 1, search, sacrificeId));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setFarms([]);
                        setMarket_avg_price(0);
                        setPageNumber(1)
                        setLastPageNumber(1)
                        // setLogo('')
                        // setLink('')
                        // setFarmId(0)
                    }
                    else {
                        if (props.route.params.farmId) {
                            setLogo(props.route.params.logo)
                            setLink(props.route.params.link && props.route.params.link)
                            // setLink(farmss[0] && farmss[0].camera_url)
                            setFarmId(props.route.params.farmId)
                            setCategoryName(props.route.params.sheepCategoryName)
                            setTypeName(props.route.params.sheepTypeName)
                            setWeightName(props.route.params.sheepWeightName)
                            setScrollToIndexx(props.route.params.scrollToIndex)
                            // setSacrificeId(sacrificeId)
                            setActiveType(
                                sacrificeId == 1 ?
                                    false
                                    :
                                    true
                            );
                        } else {
                            setLogo(farmss[0] && farmss[0].image)
                            setLink(farmss[0] && farmss[0].camera_url)
                            setMarket_avg_price(response.data.market_avg_price)
                            setPageNumber(1)
                            setFarmId(farmss[0] && farmss[0].id)
                        }
                        setFarms(response.data.items)


                        setLastPageNumber(response.data.last_page)
                        setNoResault('');
                    }
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

    }

    const skipLogin = () => {
        Toaster(
            'top',
            'warning',
            Red1,
            strings('lang.login_continuou'),
            White,
            1500,
            screenHeight / 50,
        );
        props.navigation.navigate('Login')
    }

    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMoreFarms(true)
            try {
                let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, pageNumber + 1, search, sacrificeId));

                if (response.success == true) {
                    setFarms([...farms, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMoreFarms(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMoreFarms(false);
                }
            } catch (err) {
                setLoadingMoreFarms(false);
            }

        }
        else {
        }
    }


    const [keyboardIsOpen, setKeyboardIsOpen] = React.useState(false);
    Keyboard.addListener("keyboardDidShow", () => {
        setKeyboardIsOpen(true);
    });
    Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardIsOpen(false);
    });

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    const CopilotScroll = walkthroughable(ScrollView);
    const CopilotView = walkthroughable(View);

    const [showCopilot, setShowCopilot] = useState(false);

    useEffect(() => {
        const checkIfFirstLaunch = async () => {
            try {
                const hasLaunched = await AsyncStorage.getItem('hasLaunched');
                if (!hasLaunched) {
                    setShowCopilot(true);
                    await AsyncStorage.setItem('hasLaunched', 'true');
                }
            } catch (error) {
                console.error('Failed to check first launch status:', error);
            }
        };

        checkIfFirstLaunch();
    }, []);


    useEffect(() => {
        if (showCopilot) {
            props.copilotEvents.on();
            props.start();
        }
    }, [showCopilot]);

    const [showCopilot1, setShowCopilot1] = useState(false);
    useEffect(() => {

        const checkIfFirstLaunch1 = async () => {
            try {
                const hasLaunched1 = await AsyncStorage.getItem('hasLaunched1');
                if (hasLaunched1) {
                    setShowCopilot1(true);
                }
            } catch (error) {
                console.error('Failed to check first launch status:', error);
            }
        };

        checkIfFirstLaunch1();
    }, []);
    const checkIfFirstLaunch1 = async () => {
        await AsyncStorage.setItem('hasLaunched1', 'true');
        setShowCopilot1(true);
    };

    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        console.log('Loading started');
        const timer = setTimeout(() => {
            setIsLoading(false);
            console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);
    }, []);

    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>

            {loadingMore || loading
                ?
                <LoadingMore />
                :
                <View></View>
            }
            <View style={styles.headercontainer}>
                <View style={{ width: '20%', alignItems: 'center', justifyContent: 'center', paddingStart: '3%', }}>
                    <View transparent style={styles.imgContainer} >
                        <Image source={require('../images/modrek/logo.png')} style={[styles.backImage, {}]} />
                    </View>
                </View>

                <View style={{ width: '55%', justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center', }}>
                    <Pressable
                        onPress={() => {
                            if (Token) {
                                // playSound()
                                props.navigation.navigate('ComplaintsAndSuggestions')
                            } else {
                                skipLogin()
                            }
                        }}
                        style={[styles.backButton, { width: '10%', }]} >
                        <Image source={require('../images/modrek/mess1.png')} style={{ width: "100%", height: "100%", resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                    </Pressable>
                    {/* <View style={styles.input} >
                        <Input
                            value={search}
                            onChangeText={(text) => { setSearch(text); getFarmsSearch(text) }}
                            placeholderTextColor={MediumGrey}
                            placeholder={strings('lang.Search')}
                            style={styles.inputText}
                        />
                    </View> */}
                </View>




                <View style={{ flexDirection: 'row', width: '24%', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: '1%', height: '100%', }}>
                    {/* <View style={styles.backButton} >
                        <Image source={require('../images/Silal/search.png')} style={{ resizeMode: "contain", width: '75%', height: '75%', alignSelf: 'center', tintColor: White }} />
                    </View> */}
                    <Pressable onPress={() => {
                        if (Token) {
                            props.navigation.navigate('Cart')
                        } else {
                            skipLogin()
                        }
                    }} style={styles.backButton} >
                        <Image source={require('../images/modrek/33333.png')} style={{ width: "70%", height: "70%", resizeMode: "contain", alignSelf: 'center', tintColor: White }} />
                    </Pressable>
                    <Pressable onPress={() => {
                        if (Token) {
                            props.navigation.navigate('Notifications')
                        } else {
                            skipLogin()
                        }
                    }} style={styles.backButton} >
                        <Image source={require('../images/modrek/notif.png')} style={{ resizeMode: "contain", width: '60%', height: '60%', alignSelf: 'center', tintColor: White }} />
                    </Pressable>
                    <Pressable onPress={() => {
                        if (Token) {
                            props.navigation.navigate('MyProfile')
                        } else {
                            skipLogin()
                        }
                    }} style={styles.backButton} >
                        <Image source={require('../images/modrek/userrr.png')} style={{ resizeMode: "contain", width: '70%', height: '70%', alignSelf: 'center', tintColor: White }} />
                    </Pressable>
                </View>

            </View>


            <View style={{ ...styles.backgroundVideo, ...{ backgroundColor: Black } }}>
                {farmId ?
                    <>
                        {
                            isLoading ? (
                                <>
                                    <View style={{
                                        // transform: [{ rotate: '90deg' }],
                                        position: 'absolute', height: '100%', width: '100%', alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                        <Text style={{ color: White, marginBottom: 5, fontFamily: appFontBold, fontSize: screenWidth / 35 }}>{'برجاء الانتظار جاري تحميل البث...'}</Text>
                                        <CircularProgress3 size={100} strokeWidth={10}
                                            duration={Platform.OS == 'ios' ? 20000 : 8000} />

                                    </View>

                                </>
                            ) : (
                                <></>
                            )

                        }
                        <VLCPlayer
                            style={[styles.backgroundVideo]}
                            source={{
                                initType: 2,
                                hwDecoderEnabled: 1,
                                hwDecoderForced: 1,
                                uri: link,
                                initOptions: [
                                    '--no-audio',
                                    '--rtsp-tcp',
                                    '--network-caching=150',
                                    '--rtsp-caching=150',
                                    '--no-stats',
                                    '--tcp-caching=150',
                                    '--realrtsp-caching=150',
                                ],
                            }} videoAspectRatio="16:9"
                            // autoAspectRatio={true}
                            isLive={true}
                            autoplay={true}
                            autoReloadLive={true}
                            initOptions={[
                                "--rtsp-tcp",
                                "--network-caching=" + 0,
                                "--rtsp-caching=" + 0,
                                "--no-stats",
                                "--tcp-caching=" + 0,
                                "--realrtsp-caching=" + 0,
                            ]}
                            hwDecoderEnabled={1}
                            hwDecoderForced={1}
                            mediaOptions={{
                                // ':network-caching': 0,
                                // ':live-caching': 300,
                            }}
                            onError={(err) => console.log("video error:", err)}
                        // resizeMode='contain'
                        />

                        <View style={styles.infoContainer1}>

                            <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', borderRadius: 30, height: screenHeight / 35, alignItems: "center", justifyContent: "center", }}>
                                <Image source={require('../images/newIcon/liveIcon.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                            </View>

                            <View style={{
                                width: screenHeight / 26, marginTop: 10, marginStart: 5,
                                alignSelf: "center", borderRadius: screenHeight / 52, overflow: 'hidden',
                                height: screenHeight / 26, alignItems: "center", justifyContent: "center",
                                borderWidth: 1, borderColor: Red
                            }}>
                                {/* <Image source={require('../images/newIcon/logo.png')} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} /> */}
                                <Image source={{ uri: logo }} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} />
                                {/* <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text> */}
                            </View>

                        </View>
                    </>
                    :
                    <View style={{
                        position: 'absolute', zIndex: 1000000000,
                        alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%',
                    }}>
                        {/* <Text style={{
                                fontSize: screenWidth / 30,
                                fontFamily: appFontBold, color: White,
                                textAlign: 'center'
                            }}>{strings('lang.messageLive2')}</Text> */}
                    </View>
                }



            </View>

            <View style={styles.typsView}>
                <View style={{
                    height: '90%',
                    width: '25%',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                    <Pressable
                        onPress={() => {
                            setSheetType('categories');
                            setSheet(true);
                            refRbSheetTypes.current.open();
                        }}
                        style={{
                            width: screenHeight / 8,
                            height: screenHeight / 8,
                            borderRadius: screenHeight / 16,
                            // borderWidth: 1,
                            backgroundColor: White,
                            alignItems: 'center',
                            justifyContent: 'center',
                            // shadowColor: '#000',
                            // shadowOffset: { width: 0, height: 2 },
                            // shadowOpacity: 0.2,
                            // shadowRadius: 3,
                            // elevation: 5,
                        }} >
                        {sheet
                            ?
                            <Image source={require('../images/newIcon/activeTypes.png')} style={{ width: '130%', height: '100%', resizeMode: 'cover', }} />
                            :
                            <Image source={require('../images/newIcon/types1.png')} style={{ width: '130%', height: '100%', resizeMode: 'cover', }} />
                        }

                    </Pressable>
                    <Text style={{ marginTop: 3, color: Red, fontFamily: appFontTajawal, fontSize: screenWidth / 25 }}>{
                        categoryName == ''
                            ?
                            strings('lang.Types')
                            :
                            categoryName
                    }</Text>
                </View>
                <View style={{
                    height: '100%',
                    width: '25%',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                    <Pressable
                        onPress={() => {
                            setSheetType('types')
                            refRbSheetTypes.current.open();
                        }}
                        style={{
                            width: screenHeight / 6,
                            height: screenHeight / 6,
                            borderRadius: screenHeight / 12,
                            // borderWidth: 1,
                            backgroundColor: White,
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            // shadowColor: '#000',
                            // shadowOffset: { width: 0, height: 2 },
                            // shadowOpacity: 0.2,
                            // shadowRadius: 3,
                            // elevation: 5,

                        }} >
                        {sacrificeId == 3
                            ?
                            <>
                                <Image source={require('../images/newIcon/quarter1.png')} style={{ width: '120%', height: '100%', resizeMode: 'cover', }} />
                            </>
                            :
                            sacrificeId == 2
                                ?
                                <>
                                    <Image source={require('../images/newIcon/half1.png')} style={{ width: '120%', height: '100%', resizeMode: 'cover', }} />
                                </>
                                :
                                <>
                                    <Image source={require('../images/newIcon/full1.png')} style={{ width: '120%', height: '100%', resizeMode: 'cover', }} />
                                </>
                        }
                    </Pressable>
                    <Text style={{ marginTop: 3, color: Red, fontFamily: appFontTajawal, fontSize: screenWidth / 25 }}>{typeName}</Text>
                </View>
                <View style={{
                    height: '100%',
                    width: '25%',
                    alignItems: 'center',
                    justifyContent: 'center',
                }}>
                    <Pressable
                        onPress={() => {
                            setSheetType('weights');
                            setSheet1(true);
                            refRbSheetTypes.current.open();
                        }}
                        style={{
                            width: screenHeight / 8,
                            height: screenHeight / 8,
                            borderRadius: screenHeight / 16,
                            // borderWidth: 1,
                            backgroundColor: White,
                            alignItems: 'center',
                            justifyContent: 'center',
                            // shadowColor: '#000',
                            // shadowOffset: { width: 0, height: 2 },
                            // shadowOpacity: 0.2,
                            // shadowRadius: 3,
                            // elevation: 5,
                        }} >
                        {sheet1
                            ?
                            <Image source={require('../images/newIcon/activeWeights.png')} style={{ width: '130%', height: '100%', resizeMode: 'cover', }} />
                            :
                            <Image source={require('../images/newIcon/weights1.png')} style={{ width: '130%', height: '100%', resizeMode: 'cover', }} />
                        }

                    </Pressable>
                    <Text style={{ marginTop: 3, color: Red, fontFamily: appFontTajawal, fontSize: screenWidth / 25 }}>{
                        weightName == ''
                            ?
                            strings('lang.Weight')
                            :
                            weightName
                    }</Text>
                </View>
            </View>

            <View style={{
                marginTop: Platform.OS == 'ios' ? screenHeight / 6 : screenHeight / 5, marginBottom: 10, height: 1,
                width: '95%', backgroundColor: MediumGrey
            }}></View>

            {/* <View style={{
                height: screenHeight / 20, width: '100%', alignSelf: 'flex-start',
                justifyContent: 'space-between', marginTop: 0
            }}>


            <CopilotStep
                    text={strings('lang.step1')}
                    order={1}
                    name="firstStep">
                    <CopilotScroll
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ flexGrow: 1 }}
                        style={{ alignSelf: "flex-start", flexDirection: 'row', height: '100%', minWidth: screenWidth, backgroundColor: Red }}
                    >
                        <ScrollView
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                contentContainerStyle={{ flexGrow: 1 }}
                                style={{ alignSelf: "flex-start", flexDirection: 'row', height: '100%', minWidth: screenWidth, backgroundColor: Red }}
                            >
                       
                        <Pressable
                            onPress={() => { setSheepcategoriesId(null); setPageNumber(1); getFarmsWeight(null, weightRangeId, 1, sacrificeId); getWeights(null, sacrificeId); }}
                            style={[sheepcategoriesId == null ? styles.activeButton : styles.Button]} >
                            <Text style={[sheepcategoriesId == null ? styles.activeLabel : styles.label]}>{strings('lang.All')}</Text>
                        </Pressable>

                        {sheepcategories.map((item, index) => {
                            return (
                                <Pressable
                                    key={index}
                                    onPress={() => { setSheepcategoriesId(item.id); setPageNumber(1); getFarmsWeight(item.id, weightRangeId, 1, sacrificeId); getWeights(item.id, sacrificeId); }}
                                    style={[item.id == sheepcategoriesId ? styles.activeButton : styles.Button]} >
                                    <Text style={[item.id == sheepcategoriesId ? styles.activeLabel : styles.label]}>{item.name}</Text>
                                </Pressable>
                            )
                        })}
                        </ScrollView>
                    </CopilotScroll>
                </CopilotStep>

            </View> */}



            {/* <View style={{
                height: screenHeight / 18, width: '100%', alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', backgroundColor: WhiteGery,
                marginBottom: showCopilot1 ? 5 : 0
            }}>
                <CopilotStep
                    text={strings('lang.step2')}
                    order={2}
                    name="secondStep">
                    <CopilotScroll
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        // contentContainerStyle={{ flexGrow: 1 }}
                        style={{ alignSelf: "center", flexDirection: 'row', width: screenWidth, height: '50%', }}
                    >
                        <ScrollView
                        horizontal
                        // ref={scrollViewRef}
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ flexGrow: 1 }}
                        style={{ alignSelf: "center", flexDirection: 'row', width: '100%', height: '50%', }}
                    >
                        <Pressable
                            onPress={() => {
                                // if (weightRangeId == null) {
                                setWeightRangeId(null); setPageNumber(1); getFarmsWeight(sheepcategoriesId, null, 1, sacrificeId)
                                // }
                                // else {
                                //     setWeightRangeId(0); setPageNumber(1); getFarmsWeight(sheepcategoriesId, 0, 1)
                                // }
                            }}
                            style={[weightRangeId == null ? styles.activeButton1 : styles.Button1]}
                        // style={[styles.Button1]}
                        >
                            <Text style={[weightRangeId == null ? styles.activeLabel1 : styles.label1]}> {strings('lang.All')}</Text>
                        </Pressable>
                        {weightRange.map((item, index) => {
                            return (
                                <Pressable
                                    key={index}
                                    onPress={() => {
                                        // if (item.id == weightRangeId) {
                                        //     // setWeightRangeId(null); setPageNumber(1); getFarmsWeight(null, 1)
                                        // }
                                        // else {
                                        setWeightRangeId(item.id); setPageNumber(1); getFarmsWeight(sheepcategoriesId, item.id, 1, sacrificeId);
                                        // }
                                    }}
                                    style={[item.id == weightRangeId ? styles.activeButton1 : styles.Button1]}
                                >
                                    <Text style={[item.id == weightRangeId ? styles.activeLabel1 : styles.label1]}>{item.from} {strings('lang.kg')}-{item.to} {strings('lang.kg')}</Text>
                                </Pressable>

                            )
                        })}
                        </ScrollView>
                    </CopilotScroll>
                </CopilotStep>
            </View> */}

            {/* <View style={{
                height: screenHeight / 18, width: '100%', alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between',
                marginBottom: 5
            }}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    style={{ alignSelf: "center", flexDirection: 'row', width: '100%', height: '50%', }}
                >
                    <View style={{
                        height: screenHeight / 18, width: '100%', paddingHorizontal: 25, alignItems: 'center', flexDirection: 'row', alignSelf: 'center', justifyContent: 'space-between',
                    }}>
                        {Sacrifices.map((item, index) => {
                            return (

                                <Pressable
                                    onPress={() => {
                                        setActiveType(
                                            item.id == 1 ?
                                                false
                                                :
                                                true
                                        );
                                        setSacrificeId(item.id); setPageNumber(1);
                                        getFarmsWeight(sheepcategoriesId, weightRangeId, 1, item.id);
                                        getOptions(item.id); getOrderTime(item.id)
                                    }}
                                    style={[item.id == sacrificeId ? styles.activeButton3 : styles.Button3]}
                                >
                                    <Text style={[item.id == sacrificeId ? styles.activeLabel1 : styles.label1]}>{item.name}</Text>
                                </Pressable>
                            )
                        })}
                    </View>

                </ScrollView>
            </View> */}

            {showCopilot1
                ?
                <></>
                :
                sacrificeId
                    ?
                    <View style={{
                        height: screenHeight / 15, width: '100%',
                        alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between',
                        backgroundColor: WhiteYellow, marginBottom: 5, flexDirection: 'row', paddingHorizontal: 5
                    }}>
                        <View style={{ height: '100%', alignItems: 'center', justifyContent: 'center', width: '80%' }}>
                            <Text
                                style={{
                                    fontSize: screenWidth / 33,
                                    fontFamily: appFontTajawal,
                                    color: Black,
                                    textAlign: I18nManager.isRTL ? 'left' : 'right'
                                }}
                            >
                                {strings('lang.messageWarning')}
                            </Text>
                        </View>
                        <Button
                            onPress={checkIfFirstLaunch1}
                            transparent
                            style={{
                                height: '100%', width: '10%',
                                alignItems: 'center', justifyContent: 'center',
                            }}>
                            <Image source={require('../images/modrek/xxx.png')} style={{ width: '60%', height: '60%', resizeMode: 'contain', }} />
                        </Button>
                    </View>
                    :
                    <></>
            }

            {activeType ?
                <View style={{
                    height: screenHeight / 18, width: '100%',
                    alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between',
                    backgroundColor: WhiteYellow, marginBottom: 5, flexDirection: 'row', paddingHorizontal: 5
                }}>
                    <View style={{ height: '100%', alignItems: 'center', justifyContent: 'center', width: '90%' }}>
                        <Text
                            style={{
                                fontSize: screenWidth / 40,
                                fontFamily: appFontTajawal,
                                color: Black,
                                textAlign: I18nManager.isRTL ? 'left' : 'right'
                            }}
                        >
                            {sacrificeId == 2 ?
                                'سيتم إضافة نصف من كبدة الذبيحة ونصف من الكرشة و المصارين و بدون اضافة رأس .'
                                :
                                'سيتم إضافة ربع من كبدة الذبيحة وربع من المصارين والكرشة و بدون اضافة رأس .'
                            }
                        </Text>
                    </View>
                    <Button
                        onPress={() => { setActiveType(false) }}
                        transparent
                        style={{
                            height: '100%', width: '10%',
                            alignItems: 'center', justifyContent: 'center',
                        }}>
                        <Image source={require('../images/modrek/xxx.png')} style={{ width: '60%', height: '60%', resizeMode: 'contain', }} />
                    </Button>
                </View>

                :
                <></>
            }
            <View style={styles.storeView}>
                <Image source={require('../images/newIcon/storesIcon.png')} style={{ width: '15%', height: '80%', resizeMode: 'contain', }} />

                <Text numberOfLines={1} style={{ fontSize: screenWidth / 15, fontFamily: appFontTajawal, color: Red1, alignSelf: 'center', marginHorizontal: 1 }}>{strings('lang.stores')}</Text>
            </View>

            {
                noResault ? (
                    <Text
                        style={{
                            fontSize: screenWidth / 18,
                            fontFamily: appFontBold,
                            color: Red,
                            marginTop: screenHeight / 5,
                            alignSelf: 'center',
                        }}
                    >
                        {strings('lang.No_Results')}
                    </Text>
                ) : (
                    <View></View>
                )
            }



            <FlatList
                data={farms}
                showsVerticalScrollIndicator={false}
                ref={flatListRef}
                refreshControl={
                    <RefreshControl refreshing={Reload} onRefresh={() => { onRefresh() }} />
                }
                renderItem={({ item, index }) =>
                    <CopilotStep
                        text={strings('lang.step3')}
                        order={3}
                        name="thirdStep">
                        <CopilotView
                        // style={styles.copilot}
                        >
                            <FarmContainer index={index} activeLive={activeLive}
                                openLivePress={() => {
                                    setFarmId(item.id)
                                    setItem(item)
                                    props.navigation.replace('Home',
                                        {
                                            scrollToIndex: index,
                                            logo: item.image,
                                            sheepTypeId: sacrificeId,
                                            weightId: weightRangeId,
                                            sheepCategory: sheepcategoriesId,
                                            sheepCategoryName: categoryName,
                                            sheepTypeName: typeName,
                                            sheepWeightName: weightName,
                                            farmId: item.id,
                                            link: item.camera_url,
                                        },)
                                }}
                                farmId={farmId}
                                sheepTypeId={sacrificeId}
                                imagePress={() => { setIsVisible(true); setItem(item) }}
                                press={() => { setActiveLive(item.id) }} item={item}
                                detailsPress={async () => { await setItem(item); refRbSheet.current.open() }}
                                livePress={() => {
                                    props.navigation.push('Live', {
                                        screen: 'Home',
                                        item: item,
                                        scrollToIndex: index,
                                        link: item.camera_url,
                                        sheepTypeId: sacrificeId,
                                        scrollToIndex: index,
                                        logo: item.image,
                                        weightId: weightRangeId,
                                        sheepCategory: sheepcategoriesId,
                                        sheepCategoryName: categoryName,
                                        sheepTypeName: typeName,
                                        sheepWeightName: weightName,
                                        farmId: item.id,
                                    })
                                }}
                                traderPress={() => { props.navigation.push('TraderAllContent', { trader_id: item.trader_id, farms: farms }) }} />
                            <>

                            </>
                        </CopilotView>
                    </CopilotStep>
                }
                // keyExtractor={item => item.id}
                keyExtractor={(item, index) => index.toString()}
                style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, paddingTop: 10 }}
                onEndReachedThreshold={0.1}
                onEndReached={LoadMore}
            // numColumns={2}
            />
            {loadingMoreFarms ? <ActivityIndicator style={{}} /> : <View></View>}
            {/* </ScrollView> */}
            {/* </ScrollView> */}
            <RBSheet
                ref={refRbSheet}
                height={screenHeight / 1.5}
                openDuration={250}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        borderTopEndRadius: 25,
                        borderTopStartRadius: 25,
                        width: '100%',
                        alignSelf: 'center',
                        // bottom: '25%'
                    },
                    wrapper: {

                    },
                    draggableIcon: {
                        width: '25%',
                        backgroundColor: DarkGrey
                    }
                }}
                closeOnDragDown={true}
                closeOnPressMask={false}
                closeOnPressBack={false}

                onClose={() => { setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }}
            >
                <View style={{ flexDirection: 'column', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', paddingBottom: '1%' }}>

                    <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'space-between', }}>
                        <Text style={styles.text}> {strings('lang.thedetails')}</Text>
                        <Button transparent onPress={() => { refRbSheet.current.close(); setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/modrek/x.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', tintColor: Red1 }} />
                        </Button>
                    </View>

                    <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                    <View style={{ height: screenHeight / 18, flexDirection: "row", alignItems: 'center', alignSelf: 'flex-start' }}>
                        <StarRating
                            disabled={true}
                            maxStars={5}
                            starSize={screenHeight / 40}
                            starStyle={{ marginEnd: 2, alignSelf: 'flex-start' }}
                            rating={item.rating}
                            fullStarColor={DarkYellow}
                        />
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{`(${item.rating_count})`} </Text>
                    </View>

                    <View style={{ width: '110%', height: 3, backgroundColor: WhiteGery, }}></View>
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginBottom: 3 }}>{strings('lang.sheepAvailable')}</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ minHeight: screenHeight / 16, alignSelf: 'flex-start', }}>
                        {item.sheep && item.sheep.map((item, index) => (
                            <Pressable
                                onPress={() => {
                                    setViewId(1);
                                    setIddd(item.id);
                                    setSheep(item);
                                    let sheepImages = [];
                                    for (let itemm of item.images) {
                                        sheepImages = [...sheepImages, { url: itemm }]
                                    };
                                    setSheepImages(sheepImages)
                                }}
                                style={{ backgroundColor: iddd == item.id ? LightGreen : null, width: screenWidth / 9, height: screenHeight / 16, borderTopEndRadius: 10, borderTopStartRadius: 10, paddingTop: 5, alignItems: 'center' }}
                            >
                                <View style={{
                                    width: screenWidth / 10, height: screenWidth / 10, borderRadius: screenWidth / 20, backgroundColor: item.id == iddd ? item.collar_color && item.collar_color : White, alignItems: 'center', justifyContent: 'center'
                                }}>
                                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}>{index + 1}</Text> */}
                                    {item.collar_color &&
                                        item.collar_color == 'black' ?
                                        item.id == iddd
                                            ?
                                            <Image source={require('../images/newIcon/activeBlack.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                            :
                                            <Image source={require('../images/newIcon/black.png')} style={{
                                                width: '100%', height: '100%', resizeMode: 'contain',
                                            }} />
                                        :
                                        item.collar_color == 'green' ?
                                            item.id == iddd
                                                ?
                                                <Image source={require('../images/newIcon/activeGreen.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                :
                                                <Image source={require('../images/newIcon/green.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                            :
                                            item.collar_color == 'red' ?
                                                item.id == iddd
                                                    ?
                                                    <Image source={require('../images/newIcon/activeRed.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                    :
                                                    <Image source={require('../images/newIcon/red.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                :
                                                item.collar_color == 'blue' ?
                                                    item.id == iddd
                                                        ?
                                                        <Image source={require('../images/newIcon/activeBlue.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                        :
                                                        <Image source={require('../images/newIcon/blue.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                    :
                                                    item.collar_color == 'orange' ?
                                                        item.id == iddd
                                                            ?
                                                            <Image source={require('../images/newIcon/activeOrange.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                            :
                                                            <Image source={require('../images/newIcon/orange.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                        :
                                                        item.collar_color == 'gold' ?
                                                            item.id == iddd
                                                                ?
                                                                <Image source={require('../images/newIcon/activeGold.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                                :
                                                                <Image source={require('../images/newIcon/gold.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />
                                                            :
                                                            <></>

                                    }

                                </View>
                            </Pressable>
                        ))}
                    </ScrollView>

                    {viewId == 1
                        ?
                        <>

                            <Modal visible={visible}
                                transparent={true}
                                onRequestClose={() => setIsVisible(false)}
                                animationType="fade"
                            >
                                <View style={{
                                    flex: 1,
                                    width: screenWidth,
                                    height: screenHeight,
                                    backgroundColor: White,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>

                                    <Button transparent onPress={() => { setIsVisible(false); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                                        <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                                    </Button>
                                    <FlatList
                                        data={sheepImages}
                                        keyExtractor={(item) => item.id}
                                        horizontal
                                        pagingEnabled
                                        showsHorizontalScrollIndicator={false}
                                        // initialScrollIndex={selectedIndex}
                                        style={{ width: screenWidth, height: '100%' }}
                                        getItemLayout={(data, index) => ({
                                            length: screenWidth, // Width of each image
                                            offset: screenWidth * index,
                                            index,
                                        })}
                                        renderItem={({ item }) => (
                                            <Image source={{ uri: item.url }} style={{
                                                width: screenWidth,
                                                height: screenHeight,
                                                resizeMode: 'contain'
                                                // marginHorizontal: 10,
                                                // borderRadius: 10,
                                            }} />
                                        )}
                                    />
                                </View>

                            </Modal>

                            <View style={{ backgroundColor: LightGreen, width: '100%', borderRadius: 0, paddingTop: 10, flexDirection: 'row', minHeight: screenHeight / 5 }}>
                                <View style={{ width: '20%', height: '100%', alignItems: 'center' }}>
                                    <Image source={require('../images/modrek/qr.png')} style={{ width: '100%', height: screenHeight / 13, resizeMode: 'contain', }} />
                                    {sheepImages.length == 0
                                        ?
                                        <></>
                                        :
                                        <Pressable onPress={() => { setIsVisible(true); console.log('sheep.images', sheepImages); }} style={{ width: screenWidth / 6.5, height: screenHeight / 13, resizeMode: 'contain', borderRadius: 5 }}>
                                            <Image source={{ uri: sheepImages[0].url }} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', borderRadius: 5 }} />
                                            <Image source={require('../images/modrek/scanner2.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', position: 'absolute', top: '15%', start: '15%' }} />
                                        </Pressable>
                                    }

                                </View>
                                <View style={{ width: '80%', height: '100%', alignItems: 'center', paddingEnd: 10, paddingBottom: 10 }}>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Weight')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>
                                                {`${item.weight && sacrificeId == 2 ? (item.weight.from / 2) : sacrificeId == 3 ? (item.weight.from / 4) : item.weight.from}
                                             kg -${item.weight && sacrificeId == 2 ? (item.weight.to / 2) : sacrificeId == 3 ? (item.weight.to / 4) : item.weight.to} kg`}
                                            </Text>
                                        </View>
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Gender')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{item.sheep_category && item.sheep_category.name}</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>

                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.sealcolor')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.stamp_color}</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.collarcolor')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: sheep.collar_color, alignSelf: 'flex-start' }}>{sheep.collar_color}</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.referencenumber')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.id}</Text>
                                        </View>
                                    </View>
                                    <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.price')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sacrificeId == 2 ? (sheep.price / 2) : sacrificeId == 3 ? (sheep.price / 4) : sheep.price}</Text>
                                        </View>
                                    </View>
                                </View>
                            </View>

                            <Button
                                onPress={() => {
                                    refRbSheet.current.close();
                                    props.navigation.push('Live', {
                                        screen: 'Home',
                                        item: item,
                                        link: item.camera_url,
                                        sheepTypeId: sacrificeId,
                                        scrollToIndex: scrollToIndexx,
                                        logo: item.image,
                                        weightId: weightRangeId,
                                        sheepCategory: sheepcategoriesId,
                                        sheepCategoryName: categoryName,
                                        sheepTypeName: typeName,
                                        sheepWeightName: weightName,
                                        farmId: item.id,
                                    })
                                }}
                                style={styles.buttonContainer}>
                                <Text style={styles.buttonText}>{strings('lang.Gotobookingg')}</Text>
                            </Button>
                        </>
                        :
                        <></>
                    }
                </View>

            </RBSheet>

            <RBSheet
                ref={refRbSheetTypes}
                height={screenHeight / 2}
                openDuration={250}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        borderTopEndRadius: 25,
                        borderTopStartRadius: 25,
                        width: '100%',
                        alignSelf: 'center',
                        // bottom: '25%'
                    },
                    wrapper: {

                    },
                    draggableIcon: {
                        width: '25%',
                        backgroundColor: DarkGrey
                    }
                }}
                closeOnDragDown={true}
                closeOnPressMask={true}
                closeOnPressBack={true}

                onClose={() => { setSheet(false); setSheet1(false) }}
            >
                <View style={{ flexDirection: 'column', width: '100%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>

                    {sheetType == 'categories'
                        ?
                        <ScrollView showsVerticalScrollIndicator={false} style={{ alignSelf: 'center', width: '95%', height: '100%' }}>
                            <Pressable
                                onPress={() => {
                                    setSheepcategoriesId(null); setSheet(false);
                                    setPageNumber(1); setCategoryName(''); setWeightName('');
                                    getFarmsWeight(null, weightRangeId, 1, sacrificeId);
                                    getWeights(null, sacrificeId); refRbSheetTypes.current.close()
                                }}
                                style={[sheepcategoriesId == null ? styles.activeButton : styles.Button]} >
                                <Text style={[sheepcategoriesId == null ? styles.activeLabel : styles.label]}>{strings('lang.All')}</Text>
                            </Pressable>

                            {sheepcategories.map((item, index) => {
                                return (
                                    <Pressable
                                        key={index}
                                        onPress={() => {
                                            setSheepcategoriesId(item.id); setCategoryName(item.name); setWeightName(''); setSheet(false);
                                            setPageNumber(1); getFarmsWeight(item.id, weightRangeId, 1, sacrificeId);
                                            getWeights(item.id, sacrificeId); refRbSheetTypes.current.close()
                                        }}
                                        style={[item.id == sheepcategoriesId ? styles.activeButton : styles.Button]} >
                                        <Text style={[item.id == sheepcategoriesId ? styles.activeLabel : styles.label]}>{item.name}</Text>
                                    </Pressable>
                                )
                            })}
                        </ScrollView>
                        :
                        sheetType == 'weights'
                            ?
                            <View style={{
                                flexDirection: 'row',
                                width: '100%',
                                justifyContent: 'space-between',
                                flexWrap: 'wrap',
                                padding: 5,
                                alignItems: 'center',
                                marginTop: '20%'
                            }}>
                                <Pressable
                                    onPress={() => {
                                        // if (weightRangeId == null) {
                                        setWeightRangeId(null); setPageNumber(1); getFarmsWeight(sheepcategoriesId, null, 1, sacrificeId);
                                        refRbSheetTypes.current.close(); setWeightName(''); setSheet1(false);
                                        // }
                                        // else {
                                        //     setWeightRangeId(0); setPageNumber(1); getFarmsWeight(sheepcategoriesId, 0, 1)
                                        // }
                                    }}
                                    style={[weightRangeId == null ? styles.activeButton1 : styles.Button1]}
                                // style={[styles.Button1]}
                                >
                                    <Text style={[weightRangeId == null ? styles.activeLabel1 : styles.label1]}> {strings('lang.All')}</Text>
                                </Pressable>
                                {weightRange.map((item, index) => {
                                    return (
                                        <Pressable
                                            key={index}
                                            onPress={() => {
                                                // if (item.id == weightRangeId) {
                                                //     // setWeightRangeId(null); setPageNumber(1); getFarmsWeight(null, 1)
                                                // }
                                                // else {
                                                refRbSheetTypes.current.close(); setWeightName(`${item.from} - ${item.to}`); setSheet1(false);
                                                setWeightRangeId(item.id); setPageNumber(1); getFarmsWeight(sheepcategoriesId, item.id, 1, sacrificeId);
                                                // }
                                            }}
                                            style={[item.id == weightRangeId ? styles.activeButton1 : styles.Button1]}
                                        >
                                            <Text style={[item.id == weightRangeId ? styles.activeLabel1 : styles.label1]}>{`${item.from} - ${item.to}`} </Text>
                                            {item.id == weightRangeId
                                                ?
                                                <Image source={require('../images/newIcon/activeWeight.png')} style={{ marginEnd: 5, width: screenWidth / 25, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '5%', }} />
                                                :
                                                <Image source={require('../images/newIcon/weight.png')} style={{ marginEnd: 5, width: screenWidth / 25, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '5%', }} />
                                            }

                                        </Pressable>

                                    )
                                })}
                            </View>
                            :
                            <ScrollView showsVerticalScrollIndicator={false} style={{ alignSelf: 'center', width: '95%', height: '100%' }}>
                                {Sacrifices.map((item, index) => {
                                    return (

                                        <Pressable
                                            onPress={() => {
                                                setActiveType(
                                                    item.id == 1 ?
                                                        false
                                                        :
                                                        true
                                                );
                                                setSacrificeId(item.id); setPageNumber(1); setTypeName(item.name);
                                                getFarmsWeight(sheepcategoriesId, weightRangeId, 1, item.id);
                                                getOptions(item.id); getOrderTime(item.id), refRbSheetTypes.current.close()
                                            }}
                                            style={[item.id == sacrificeId ? styles.activeButton : styles.Button]}
                                        >
                                            <Text style={[item.id == sacrificeId ? styles.activeLabel : styles.label]}>{item.name}</Text>
                                        </Pressable>
                                    )
                                })}
                            </ScrollView>
                    }


                    {/* <View style={{ position: 'absolute', bottom: 25, height: screenHeight / 14, width: screenWidth, backgroundColor: Red }}>
                        <Image source={require('../images/newIcon/line.png')} style={{ width: '100%', height: '20%', resizeMode: 'contain', }} />
                    </View> */}
                </View>

            </RBSheet>
            <View style={{
                marginTop: screenHeight / 10
            }}></View>
            <MyFooter current={'Home'} navigation={props.navigation} />

        </View >

    )
}

// }

export default copilot({
    tooltipComponent: MyComponent, // Set the custom tooltip component
    overlay: 'view',
    animated: true,
    // backdropColor: Black,
    androidStatusBarVisible: true,
    stopOnOutsideClick: false,
    // verticalOffset: 20
})(Home);
const styles = StyleSheet.create({
    storeView: {
        flexDirection: 'row',
        width: '95%',
        height: screenHeight / 20,
        alignItems: 'center',
    },
    typsView: {
        flexDirection: 'row',
        width: '95%',
        height: screenHeight / 5,
        // backgroundColor: Red1,
        position: 'absolute',
        zIndex: 200000,
        top: Platform.OS == 'ios' ? screenHeight / 3.2 : screenHeight / 3.8,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    copilot: {

    },
    container: {
        flexDirection: 'row',
        alignSelf: 'flex-start',
        justifyContent: 'space-between',
        marginVertical: '1.5%',

    },

    backgroundVideo: {
        width: screenWidth,
        height: screenHeight / 3.8,
        justifyContent: 'center',
        zIndex: 10000,
    },

    title: {
        fontFamily: appFont,
        fontSize: screenWidth / 28,
        color: Black,
    },
    title2: {
        fontFamily: appFont,
        fontSize: screenWidth / 28,
        color: Black,
    },
    text: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        color: Black
    },
    title1: {
        marginStart: '15%',
        fontFamily: appFont,
        fontSize: screenWidth / 28
    },

    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
    activeButton: { height: screenHeight / 18, width: '70%', marginVertical: 10, borderRadius: 25, alignItems: "center", justifyContent: "center", alignSelf: 'center', backgroundColor: Red, },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: 'white', },
    Button: { height: screenHeight / 18, width: '70%', marginVertical: 10, borderRadius: 25, alignItems: "center", justifyContent: "center", alignSelf: 'center', backgroundColor: WhiteGery },
    label: { fontFamily: appFont, fontSize: screenWidth / 35, color: Red, },

    activeButton1: { flexDirection: 'row', height: screenHeight / 20, marginVertical: 5, backgroundColor: Red, borderRadius: 25, width: '30%', alignItems: "center", justifyContent: "center", },
    activeLabel1: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: White, marginHorizontal: 10 },
    Button1: { flexDirection: 'row', height: screenHeight / 20, marginVertical: 5, backgroundColor: MediumGrey, borderRadius: 25, width: '30%', alignItems: "center", justifyContent: "center", },
    label1: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: 'black', marginHorizontal: 10 },
    label2: { fontFamily: appFont, fontSize: screenWidth / 35, color: 'black', end: "90%", marginVertical: '7%' },
    activeButton3: { height: '60%', backgroundColor: Red, borderRadius: 25, width: '32%', alignItems: "center", justifyContent: "center", },
    Button3: { height: '60%', backgroundColor: MediumGrey, borderRadius: 25, width: '32%', alignItems: "center", justifyContent: "center", borderColor: WhiteGery, borderWidth: 1 },

    Button2: { height: '80%', width: screenWidth / 2.5, flexDirection: 'row', backgroundColor: WhiteGery, borderTopRightRadius: 20, borderBottomRightRadius: 20, alignItems: 'flex-start', justifyContent: 'flex-start', alignSelf: 'flex-start', borderColor: WhiteGery, borderWidth: 1 },
    headercontainer: {
        backgroundColor: Red, height: Platform.OS == 'ios' ? screenHeight / 10 : screenHeight / 18, alignSelf: 'flex-start', alignItems: 'center', flexDirection: "row", width: '100%', paddingTop: Platform.OS == 'ios' ? screenHeight / 18 : 10
    },


    title: {
        fontFamily: appFontBold, color: Black, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    title2: {
        fontFamily: appFontBold, color: Red, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    imgContainer: {
        width: '100%', height: '100%', justifyContent: "center",
    },
    backButton: {
        width: '33%', height: '100%', justifyContent: "center",
    },
    backImage: {
        width: "80%", height: "90%", resizeMode: "contain",
    },
    drawerImage: {
        width: "90%", height: "70%", resizeMode: "contain", alignSelf: 'center'
    },
    infoContainer1: {
        width: screenWidth,
        paddingHorizontal: 25,
        height: screenHeight / 35,
        // marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'absolute', zIndex: 100000, top: 10, end: 10
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '100%',
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '5%',

    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    input: {
        height: screenHeight / 30, borderWidth: 1, borderRadius: 20, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "85%", marginHorizontal: '2%'
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: White, width: '100%', fontFamily: appFontBold,
        fontSize: screenWidth / 40, alignSelf: 'center', marginHorizontal: '2%'
    },
    icon: { width: '50%', height: '50%', resizeMode: 'contain', },
    imageActive: { height: 80, width: screenWidth / 6, borderColor: MediumGrey, borderRadius: 15, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
    imageUnactive: { height: 75, width: screenWidth / 6, borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
});